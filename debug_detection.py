import os
import cv2
import numpy as np
from datetime import datetime
from PIL import ImageGrab
from ultralytics import YOLO
import easyocr

# === CONFIG ===
CROP_BOX = (970, 388, 1350, 632)
CARD_MODEL_PATH = "detect_yolo_one.pt"
WINNING_SEAT_MODEL_PATH = "winning_seat.pt"

def test_screenshot():
    """Test screenshot capture and save for debugging"""
    print("📸 Testing screenshot capture...")
    
    # Capture screenshot
    img = ImageGrab.grab(bbox=CROP_BOX)
    img_np = np.array(img)
    img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
    
    # Save for debugging
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    debug_path = f"debug_screenshot_{timestamp}.jpg"
    cv2.imwrite(debug_path, img_bgr)
    
    print(f"✅ Screenshot saved: {debug_path}")
    print(f"📏 Image shape: {img_bgr.shape}")
    print(f"📦 Crop box: {CROP_BOX}")
    
    return img_bgr, debug_path

def test_yolo_models(image):
    """Test both YOLO models"""
    print("\n🎯 Testing YOLO models...")
    
    # Test card detection model
    try:
        print(f"Loading card model: {CARD_MODEL_PATH}")
        card_model = YOLO(CARD_MODEL_PATH)
        print(f"✅ Card model loaded successfully")
        print(f"📋 Card model classes: {list(card_model.names.values())}")
        
        # Run detection
        results = card_model(image, verbose=True, conf=0.1)  # Lower confidence for testing
        print(f"🔍 Card detections found: {len(results[0].boxes) if results[0].boxes else 0}")
        
        if results[0].boxes:
            for i, box in enumerate(results[0].boxes):
                cls_id = int(box.cls[0])
                confidence = float(box.conf[0])
                label = card_model.names[cls_id]
                print(f"   Detection {i+1}: {label} ({confidence:.3f})")
        
    except Exception as e:
        print(f"❌ Card model error: {e}")
    
    # Test winning seat model
    try:
        print(f"\nLoading winning seat model: {WINNING_SEAT_MODEL_PATH}")
        seat_model = YOLO(WINNING_SEAT_MODEL_PATH)
        print(f"✅ Winning seat model loaded successfully")
        print(f"📋 Seat model classes: {list(seat_model.names.values())}")
        
        # Run detection
        results = seat_model(image, verbose=True, conf=0.1)
        print(f"🔍 Seat detections found: {len(results[0].boxes) if results[0].boxes else 0}")
        
        if results[0].boxes:
            for i, box in enumerate(results[0].boxes):
                cls_id = int(box.cls[0])
                confidence = float(box.conf[0])
                label = seat_model.names[cls_id]
                xyxy = box.xyxy[0].cpu().numpy()
                x_center = (xyxy[0] + xyxy[2]) / 2
                print(f"   Detection {i+1}: {label} ({confidence:.3f}) at x={x_center:.1f}")
        
    except Exception as e:
        print(f"❌ Winning seat model error: {e}")

def test_easyocr(image):
    """Test EasyOCR"""
    print("\n👁️ Testing EasyOCR...")
    
    try:
        reader = easyocr.Reader(['en'])
        results = reader.readtext(image)
        
        print(f"🔍 EasyOCR found {len(results)} text regions:")
        for i, (bbox, text, confidence) in enumerate(results):
            print(f"   Text {i+1}: '{text}' ({confidence:.3f})")
            
    except Exception as e:
        print(f"❌ EasyOCR error: {e}")

def test_paddleocr(image):
    """Test PaddleOCR with proper API"""
    print("\n🔤 Testing PaddleOCR...")
    
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_textline_orientation=True, lang='en')
        
        results = ocr.ocr(image)
        
        if results and results[0]:
            print(f"🔍 PaddleOCR found {len(results[0])} text regions:")
            for i, line in enumerate(results[0]):
                text = line[1][0]
                confidence = line[1][1]
                print(f"   Text {i+1}: '{text}' ({confidence:.3f})")
        else:
            print("🔍 PaddleOCR found no text")
            
    except Exception as e:
        print(f"❌ PaddleOCR error: {e}")

def main():
    print("🔧 Debug Detection System")
    print("=" * 50)
    
    # Test screenshot
    image, debug_path = test_screenshot()
    
    # Test all detection methods
    test_yolo_models(image)
    test_easyocr(image)
    test_paddleocr(image)
    
    print(f"\n✅ Debug complete. Check {debug_path} to see what was captured.")
    print("💡 If no detections found, the screen area might be empty or models need different confidence thresholds.")

if __name__ == "__main__":
    main()
