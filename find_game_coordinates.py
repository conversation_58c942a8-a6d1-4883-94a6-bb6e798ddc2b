import cv2
import numpy as np
from PIL import ImageGrab
import tkinter as tk
from tkinter import messagebox

def capture_full_screen():
    """Capture full screen and let user select game area"""
    
    # Capture full screen
    screenshot = ImageGrab.grab()
    screenshot_np = np.array(screenshot)
    screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
    
    # Save full screenshot for reference
    cv2.imwrite("full_screen_capture.jpg", screenshot_bgr)
    print("📸 Full screen captured and saved as 'full_screen_capture.jpg'")
    
    return screenshot_bgr

def mouse_callback(event, x, y, flags, param):
    """Handle mouse events for coordinate selection"""
    global start_point, end_point, drawing, screenshot_copy
    
    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        start_point = (x, y)
        end_point = (x, y)
    
    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            screenshot_copy = screenshot.copy()
            end_point = (x, y)
            cv2.rectangle(screenshot_copy, start_point, end_point, (0, 255, 0), 2)
            
            # Show coordinates
            coord_text = f"({start_point[0]}, {start_point[1]}) to ({end_point[0]}, {end_point[1]})"
            cv2.putText(screenshot_copy, coord_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            cv2.imshow("Select Game Area", screenshot_copy)
    
    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        end_point = (x, y)
        
        # Final rectangle
        cv2.rectangle(screenshot_copy, start_point, end_point, (0, 255, 0), 2)
        coord_text = f"CROP_BOX = ({start_point[0]}, {start_point[1]}, {end_point[0]}, {end_point[1]})"
        cv2.putText(screenshot_copy, coord_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(screenshot_copy, "Press 's' to save, 'r' to reset, 'q' to quit", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        cv2.imshow("Select Game Area", screenshot_copy)

def test_crop_area(crop_box):
    """Test the selected crop area"""
    print(f"\n🧪 Testing crop area: {crop_box}")
    
    # Capture the cropped area
    cropped = ImageGrab.grab(bbox=crop_box)
    cropped_np = np.array(cropped)
    cropped_bgr = cv2.cvtColor(cropped_np, cv2.COLOR_RGB2BGR)
    
    # Save cropped image
    cv2.imwrite("test_crop.jpg", cropped_bgr)
    print(f"✅ Cropped area saved as 'test_crop.jpg'")
    print(f"📏 Cropped image size: {cropped_bgr.shape}")
    
    # Show the cropped area
    cv2.imshow("Cropped Game Area", cropped_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def main():
    global screenshot, screenshot_copy, start_point, end_point, drawing
    
    print("🎯 Game Area Coordinate Finder")
    print("=" * 50)
    print("Instructions:")
    print("1. Make sure your card game is visible on screen")
    print("2. Click and drag to select the game area")
    print("3. Press 's' to save coordinates")
    print("4. Press 'r' to reset selection")
    print("5. Press 'q' to quit")
    print("=" * 50)
    
    # Initialize variables
    start_point = (0, 0)
    end_point = (0, 0)
    drawing = False
    
    # Capture full screen
    screenshot = capture_full_screen()
    screenshot_copy = screenshot.copy()
    
    # Resize for display if too large
    height, width = screenshot.shape[:2]
    if width > 1920 or height > 1080:
        scale = min(1920/width, 1080/height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        screenshot = cv2.resize(screenshot, (new_width, new_height))
        screenshot_copy = screenshot.copy()
        print(f"📏 Resized display to {new_width}x{new_height} for viewing")
    
    # Create window and set mouse callback
    cv2.namedWindow("Select Game Area", cv2.WINDOW_NORMAL)
    cv2.setMouseCallback("Select Game Area", mouse_callback)
    
    # Instructions on image
    cv2.putText(screenshot_copy, "Click and drag to select game area", (10, height-60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(screenshot_copy, "Press 's' to save, 'r' to reset, 'q' to quit", (10, height-30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    cv2.imshow("Select Game Area", screenshot_copy)
    
    while True:
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('s'):  # Save coordinates
            if start_point != end_point:
                # Ensure coordinates are in correct order
                x1, y1 = min(start_point[0], end_point[0]), min(start_point[1], end_point[1])
                x2, y2 = max(start_point[0], end_point[0]), max(start_point[1], end_point[1])
                
                crop_box = (x1, y1, x2, y2)
                print(f"\n✅ Coordinates saved!")
                print(f"📋 Copy this to your detection script:")
                print(f"CROP_BOX = {crop_box}")
                
                # Test the crop area
                cv2.destroyAllWindows()
                test_crop_area(crop_box)
                
                # Save coordinates to file
                with open("game_coordinates.txt", "w") as f:
                    f.write(f"CROP_BOX = {crop_box}\n")
                    f.write(f"# Left: {x1}, Top: {y1}, Right: {x2}, Bottom: {y2}\n")
                    f.write(f"# Width: {x2-x1}, Height: {y2-y1}\n")
                
                print(f"💾 Coordinates also saved to 'game_coordinates.txt'")
                break
            else:
                print("⚠️ Please select an area first!")
        
        elif key == ord('r'):  # Reset
            screenshot_copy = screenshot.copy()
            start_point = (0, 0)
            end_point = (0, 0)
            cv2.putText(screenshot_copy, "Click and drag to select game area", (10, height-60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(screenshot_copy, "Press 's' to save, 'r' to reset, 'q' to quit", (10, height-30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.imshow("Select Game Area", screenshot_copy)
            print("🔄 Selection reset")
        
        elif key == ord('q'):  # Quit
            print("👋 Exiting...")
            break
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
