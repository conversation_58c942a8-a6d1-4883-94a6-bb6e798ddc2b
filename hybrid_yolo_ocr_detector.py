import os
import cv2
import time
import numpy as np
from datetime import datetime
from PIL import ImageGrab
from ultralytics import <PERSON><PERSON><PERSON>
from paddleocr import PaddleOCR
import sqlite3

# === CONFIG ===
CROP_BOX = (970, 388, 1350, 632)
MODEL_PATH = "detect_yolo_one.pt"
DB_PATH = "game_analysis.db"
TABLE_NAME = "hybrid_detections"
DELAY_INITIAL = 14.5
DELAY_NEXT = 56
CONFIDENCE_THRESHOLD = 0.50  # Lower threshold for verification candidates

# High-accuracy classes (>90% precision) - trust these directly
HIGH_ACCURACY_CLASSES = {
    'hearts', 'high_card', '8', '9', 'A', 'J', 'blue_seat', 'spades'
}

# Lower-accuracy classes that need OCR verification
VERIFY_WITH_OCR_CLASSES = {
    '10': ['10', 'IO', '1O', 'O1'],  # Common OCR variations
    '2': ['2'],
    '3': ['3'],
    '4': ['4'],
    '6': ['6', 'G'],
    '7': ['7'],
    'Q': ['Q', 'O']
}

# === Load Model and OCR ===
model = YOLO(MODEL_PATH)
ocr = PaddleOCR(use_angle_cls=True, lang='en')

# === SQLite DB ===
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create table for hybrid detections
cursor.execute(f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TEXT,
        label TEXT,
        confidence REAL,
        seat TEXT,
        bbox_x1 REAL,
        bbox_y1 REAL,
        bbox_x2 REAL,
        bbox_y2 REAL,
        verification_method TEXT,
        ocr_text TEXT,
        ocr_confidence REAL,
        verified BOOLEAN
    )
''')
conn.commit()

def get_seat_from_x(x):
    if x < 110: return 'A'
    elif x < 230: return 'B'
    else: return 'C'

def verify_with_ocr(image, bbox, predicted_label):
    """Use OCR to verify YOLO detection"""
    try:
        # Extract the bounding box region
        x1, y1, x2, y2 = map(int, bbox)
        
        # Add padding around the detection
        padding = 10
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(image.shape[1], x2 + padding)
        y2 = min(image.shape[0], y2 + padding)
        
        roi = image[y1:y2, x1:x2]
        
        # Skip if ROI is too small
        if roi.shape[0] < 10 or roi.shape[1] < 10:
            return False, "", 0.0
        
        # Run OCR on the region
        ocr_results = ocr.ocr(roi, cls=True)
        
        if not ocr_results or not ocr_results[0]:
            return False, "", 0.0
        
        # Extract text and confidence
        best_text = ""
        best_confidence = 0.0
        
        for line in ocr_results[0]:
            text = line[1][0].strip()
            confidence = line[1][1]
            
            if confidence > best_confidence:
                best_text = text
                best_confidence = confidence
        
        # Check if OCR text matches expected variations
        if predicted_label in VERIFY_WITH_OCR_CLASSES:
            expected_variations = VERIFY_WITH_OCR_CLASSES[predicted_label]
            for variation in expected_variations:
                if variation.lower() in best_text.lower() or best_text.lower() in variation.lower():
                    return True, best_text, best_confidence
        
        return False, best_text, best_confidence
        
    except Exception as e:
        print(f"❌ OCR verification error: {e}")
        return False, "", 0.0

def extract_and_verify_detections(results, image):
    """Extract YOLO detections and verify low-accuracy ones with OCR"""
    verified_detections = []
    
    if not results[0].boxes:
        return verified_detections
    
    for box in results[0].boxes:
        cls_id = int(box.cls[0])
        confidence = float(box.conf[0])
        label = model.names[cls_id]
        xyxy = box.xyxy[0].cpu().numpy()
        x_center = (xyxy[0] + xyxy[2]) / 2
        seat = get_seat_from_x(x_center)
        
        detection = {
            'label': label,
            'confidence': confidence,
            'seat': seat,
            'bbox': xyxy,
            'verification_method': '',
            'ocr_text': '',
            'ocr_confidence': 0.0,
            'verified': False
        }
        
        # High-accuracy classes - trust directly
        if label in HIGH_ACCURACY_CLASSES and confidence >= 0.90:
            detection['verification_method'] = 'high_accuracy_direct'
            detection['verified'] = True
            verified_detections.append(detection)
            print(f"✅ Direct: {label} ({confidence:.3f}) in seat {seat}")
        
        # Low-accuracy classes - verify with OCR
        elif label in VERIFY_WITH_OCR_CLASSES and confidence >= CONFIDENCE_THRESHOLD:
            is_verified, ocr_text, ocr_conf = verify_with_ocr(image, xyxy, label)
            
            detection['verification_method'] = 'ocr_verified'
            detection['ocr_text'] = ocr_text
            detection['ocr_confidence'] = ocr_conf
            detection['verified'] = is_verified
            
            if is_verified:
                verified_detections.append(detection)
                print(f"✅ OCR Verified: {label} ({confidence:.3f}) → '{ocr_text}' ({ocr_conf:.3f}) in seat {seat}")
            else:
                print(f"❌ OCR Failed: {label} ({confidence:.3f}) → '{ocr_text}' ({ocr_conf:.3f}) in seat {seat}")
        
        else:
            print(f"⚠️ Skipped: {label} ({confidence:.3f}) - Below threshold or unknown class")
    
    return verified_detections

def save_to_db(verified_detections, timestamp):
    """Save verified detections to database"""
    if not verified_detections:
        print(f"⚠️ No verified detections to save at {timestamp}")
        return
    
    for detection in verified_detections:
        cursor.execute(f'''
            INSERT INTO {TABLE_NAME} 
            (timestamp, label, confidence, seat, bbox_x1, bbox_y1, bbox_x2, bbox_y2,
             verification_method, ocr_text, ocr_confidence, verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            detection['label'],
            detection['confidence'],
            detection['seat'],
            detection['bbox'][0],
            detection['bbox'][1],
            detection['bbox'][2],
            detection['bbox'][3],
            detection['verification_method'],
            detection['ocr_text'],
            detection['ocr_confidence'],
            detection['verified']
        ))
    
    conn.commit()
    print(f"✅ Saved {len(verified_detections)} verified detections at {timestamp}")
    
    # Print summary by verification method
    direct_count = sum(1 for d in verified_detections if d['verification_method'] == 'high_accuracy_direct')
    ocr_count = sum(1 for d in verified_detections if d['verification_method'] == 'ocr_verified')
    
    print(f"   📊 Direct (high accuracy): {direct_count}")
    print(f"   🔍 OCR Verified: {ocr_count}")

# === MAIN LOOP ===
def run():
    print("🚀 Hybrid YOLO + OCR Card Detector")
    print("High accuracy classes: Direct detection")
    print("Lower accuracy classes: OCR verification")
    print("-" * 50)
    print("Press ENTER to start capture loop after countdown reaches 1...")
    input()
    time.sleep(DELAY_INITIAL)

    while True:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        img = ImageGrab.grab(bbox=CROP_BOX)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

        print(f"\n📸 Processing frame at {timestamp}")
        
        # Run YOLO detection
        results = model(img_bgr, verbose=False, conf=CONFIDENCE_THRESHOLD)
        
        # Extract and verify detections
        verified_detections = extract_and_verify_detections(results, img_bgr)
        
        # Save to database
        save_to_db(verified_detections, timestamp)
        
        print(f"📊 Total YOLO detections: {len(results[0].boxes) if results[0].boxes else 0}")
        print(f"🎯 Verified detections: {len(verified_detections)}")
        print("-" * 50)
        
        time.sleep(DELAY_NEXT)

if __name__ == "__main__":
    run()
