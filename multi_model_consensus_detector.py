import os
import cv2
import time
import numpy as np
from datetime import datetime
from PIL import Image<PERSON>rab
from ultralytics import <PERSON><PERSON><PERSON>
from paddleocr import PaddleOCR
import easyocr
import sqlite3
import re
from collections import Counter

# === CONFIG ===
CROP_BOX = (970, 388, 1350, 632)
CARD_MODEL_PATH = "detect_yolo_one.pt"
WINNING_SEAT_MODEL_PATH = "winning_seat.pt"
DB_PATH = "game_analysis.db"
TABLE_NAME = "consensus_detections"
DELAY_INITIAL = 14.5
DELAY_NEXT = 56

# === Load Models and OCR ===
card_model = YOLO(CARD_MODEL_PATH)
winning_seat_model = YOLO(WINNING_SEAT_MODEL_PATH)
paddle_ocr = PaddleOCR(use_angle_cls=True, lang='en')
easy_ocr = easyocr.Reader(['en'])

# === SQLite DB ===
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create comprehensive table for consensus results
cursor.execute(f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TEXT,
        winning_seat TEXT,
        winning_seat_confidence REAL,
        
        -- Card detection results
        yolo_cards TEXT,
        paddle_cards TEXT,
        easy_cards TEXT,
        consensus_cards TEXT,
        
        -- Pattern detection results
        yolo_patterns TEXT,
        paddle_patterns TEXT,
        easy_patterns TEXT,
        consensus_patterns TEXT,
        
        -- Confidence scores
        agreement_score REAL,
        total_detections INTEGER,
        consensus_detections INTEGER
    )
''')
conn.commit()

def detect_winning_seat(image):
    """Use winning_seat.pt model to detect brightest_cards and determine winning seat"""
    try:
        results = winning_seat_model(image, verbose=False)
        
        if not results[0].boxes:
            return None, 0.0
        
        # Find the brightest_cards detection with highest confidence
        best_detection = None
        best_confidence = 0.0
        
        for box in results[0].boxes:
            cls_id = int(box.cls[0])
            label = winning_seat_model.names[cls_id]
            confidence = float(box.conf[0])
            
            if label == 'brightest_cards' and confidence > best_confidence:
                best_confidence = confidence
                xyxy = box.xyxy[0].cpu().numpy()
                x_center = (xyxy[0] + xyxy[2]) / 2
                best_detection = x_center
        
        if best_detection is None:
            return None, 0.0
        
        # Determine seat based on x position
        image_width = image.shape[1]
        left_boundary = image_width / 3
        right_boundary = 2 * image_width / 3
        
        if best_detection < left_boundary:
            return "blue_seat", best_confidence  # Left side
        elif best_detection < right_boundary:
            return "purple_seat", best_confidence  # Center
        else:
            return "golden_seat", best_confidence  # Right side
            
    except Exception as e:
        print(f"❌ Winning seat detection error: {e}")
        return None, 0.0

def extract_yolo_predictions(image):
    """Extract card and pattern predictions from YOLO model"""
    try:
        results = card_model(image, verbose=False, conf=0.3)
        
        cards = []
        patterns = []
        
        if results[0].boxes:
            for box in results[0].boxes:
                cls_id = int(box.cls[0])
                label = card_model.names[cls_id]
                confidence = float(box.conf[0])
                
                # Categorize detections
                if label in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']:
                    cards.append(f"{label}({confidence:.2f})")
                elif label in ['hearts', 'diamonds', 'clubs', 'spades']:
                    cards.append(f"{label}({confidence:.2f})")
                elif label in ['high_card', 'pair', 'sequence', 'color']:
                    patterns.append(f"{label}({confidence:.2f})")
        
        return cards, patterns
        
    except Exception as e:
        print(f"❌ YOLO prediction error: {e}")
        return [], []

def extract_paddle_predictions(image):
    """Extract predictions from PaddleOCR"""
    try:
        results = paddle_ocr.ocr(image, cls=True)
        
        cards = []
        patterns = []
        
        if results and results[0]:
            for line in results[0]:
                text = line[1][0].strip()
                confidence = line[1][1]
                
                # Look for card numbers and suits
                card_matches = re.findall(r'[2-9JQKA10]+[♠♣♦♥]?', text.upper())
                for match in card_matches:
                    cards.append(f"{match}({confidence:.2f})")
                
                # Look for pattern keywords
                text_lower = text.lower()
                if 'pair' in text_lower:
                    patterns.append(f"pair({confidence:.2f})")
                elif 'high' in text_lower and 'card' in text_lower:
                    patterns.append(f"high_card({confidence:.2f})")
                elif 'sequence' in text_lower or 'straight' in text_lower:
                    patterns.append(f"sequence({confidence:.2f})")
                elif 'color' in text_lower or 'flush' in text_lower:
                    patterns.append(f"color({confidence:.2f})")
        
        return cards, patterns
        
    except Exception as e:
        print(f"❌ PaddleOCR prediction error: {e}")
        return [], []

def extract_easy_predictions(image):
    """Extract predictions from EasyOCR"""
    try:
        results = easy_ocr.readtext(image)
        
        cards = []
        patterns = []
        
        for (bbox, text, confidence) in results:
            text = text.strip()
            
            # Look for card numbers and suits
            card_matches = re.findall(r'[2-9JQKA10]+[♠♣♦♥]?', text.upper())
            for match in card_matches:
                cards.append(f"{match}({confidence:.2f})")
            
            # Look for pattern keywords
            text_lower = text.lower()
            if 'pair' in text_lower:
                patterns.append(f"pair({confidence:.2f})")
            elif 'high' in text_lower and 'card' in text_lower:
                patterns.append(f"high_card({confidence:.2f})")
            elif 'sequence' in text_lower or 'straight' in text_lower:
                patterns.append(f"sequence({confidence:.2f})")
            elif 'color' in text_lower or 'flush' in text_lower:
                patterns.append(f"color({confidence:.2f})")
        
        return cards, patterns
        
    except Exception as e:
        print(f"❌ EasyOCR prediction error: {e}")
        return [], []

def find_consensus(yolo_items, paddle_items, easy_items, item_type="cards"):
    """Find consensus among the three detection methods"""
    
    def normalize_item(item):
        """Extract the core item name without confidence scores"""
        return re.sub(r'\([^)]*\)', '', item).strip().upper()
    
    # Normalize all items
    all_items = []
    for items in [yolo_items, paddle_items, easy_items]:
        all_items.extend([normalize_item(item) for item in items])
    
    if not all_items:
        return []
    
    # Count occurrences
    item_counts = Counter(all_items)
    
    # Find items that appear in at least 2 out of 3 methods
    consensus_items = []
    for item, count in item_counts.items():
        if count >= 2:  # Appears in at least 2 methods
            consensus_items.append(f"{item}(consensus:{count}/3)")
    
    return consensus_items

def calculate_agreement_score(yolo_items, paddle_items, easy_items):
    """Calculate how much the three methods agree"""
    
    def normalize_items(items):
        return set(re.sub(r'\([^)]*\)', '', item).strip().upper() for item in items)
    
    yolo_set = normalize_items(yolo_items)
    paddle_set = normalize_items(paddle_items)
    easy_set = normalize_items(easy_items)
    
    # Calculate intersection over union
    intersection = len(yolo_set & paddle_set & easy_set)
    union = len(yolo_set | paddle_set | easy_set)
    
    if union == 0:
        return 0.0
    
    return intersection / union

def process_frame(image):
    """Process a single frame with all detection methods"""
    
    print("🔍 Running multi-model detection...")
    
    # 1. Detect winning seat
    winning_seat, seat_confidence = detect_winning_seat(image)
    print(f"🏆 Winning seat: {winning_seat} ({seat_confidence:.3f})")
    
    # 2. Extract predictions from all three methods
    yolo_cards, yolo_patterns = extract_yolo_predictions(image)
    paddle_cards, paddle_patterns = extract_paddle_predictions(image)
    easy_cards, easy_patterns = extract_easy_predictions(image)
    
    print(f"🎯 YOLO: {len(yolo_cards)} cards, {len(yolo_patterns)} patterns")
    print(f"🔤 Paddle: {len(paddle_cards)} cards, {len(paddle_patterns)} patterns")
    print(f"👁️ Easy: {len(easy_cards)} cards, {len(easy_patterns)} patterns")
    
    # 3. Find consensus
    consensus_cards = find_consensus(yolo_cards, paddle_cards, easy_cards, "cards")
    consensus_patterns = find_consensus(yolo_patterns, paddle_patterns, easy_patterns, "patterns")
    
    print(f"🤝 Consensus: {len(consensus_cards)} cards, {len(consensus_patterns)} patterns")
    
    # 4. Calculate agreement scores
    card_agreement = calculate_agreement_score(yolo_cards, paddle_cards, easy_cards)
    pattern_agreement = calculate_agreement_score(yolo_patterns, paddle_patterns, easy_patterns)
    overall_agreement = (card_agreement + pattern_agreement) / 2
    
    print(f"📊 Agreement: Cards {card_agreement:.2f}, Patterns {pattern_agreement:.2f}, Overall {overall_agreement:.2f}")
    
    return {
        'winning_seat': winning_seat or 'unknown',
        'winning_seat_confidence': seat_confidence,
        'yolo_cards': ', '.join(yolo_cards),
        'paddle_cards': ', '.join(paddle_cards),
        'easy_cards': ', '.join(easy_cards),
        'consensus_cards': ', '.join(consensus_cards),
        'yolo_patterns': ', '.join(yolo_patterns),
        'paddle_patterns': ', '.join(paddle_patterns),
        'easy_patterns': ', '.join(easy_patterns),
        'consensus_patterns': ', '.join(consensus_patterns),
        'agreement_score': overall_agreement,
        'total_detections': len(yolo_cards) + len(paddle_cards) + len(easy_cards) + len(yolo_patterns) + len(paddle_patterns) + len(easy_patterns),
        'consensus_detections': len(consensus_cards) + len(consensus_patterns)
    }

def save_to_db(results, timestamp):
    """Save consensus results to database"""
    
    cursor.execute(f'''
        INSERT INTO {TABLE_NAME} 
        (timestamp, winning_seat, winning_seat_confidence, yolo_cards, paddle_cards, easy_cards, consensus_cards,
         yolo_patterns, paddle_patterns, easy_patterns, consensus_patterns, agreement_score, total_detections, consensus_detections)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        timestamp,
        results['winning_seat'],
        results['winning_seat_confidence'],
        results['yolo_cards'],
        results['paddle_cards'],
        results['easy_cards'],
        results['consensus_cards'],
        results['yolo_patterns'],
        results['paddle_patterns'],
        results['easy_patterns'],
        results['consensus_patterns'],
        results['agreement_score'],
        results['total_detections'],
        results['consensus_detections']
    ))
    
    conn.commit()
    print(f"✅ Consensus results saved to database")

# === MAIN LOOP ===
def run():
    print("🚀 Multi-Model Consensus Card Detector")
    print("Models: YOLO + PaddleOCR + EasyOCR + Winning Seat Detection")
    print("-" * 60)
    print("Press ENTER to start capture loop after countdown reaches 1...")
    input()
    time.sleep(DELAY_INITIAL)

    while True:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        print(f"\n📸 Processing frame at {timestamp}")
        print("=" * 50)

        # Capture screenshot
        img = ImageGrab.grab(bbox=CROP_BOX)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

        # Process with all models
        results = process_frame(img_bgr)

        # Save results
        save_to_db(results, timestamp)

        # Show detailed results
        print("\n📋 Detection Summary:")
        print(f"🏆 Winning Seat: {results['winning_seat']} ({results['winning_seat_confidence']:.3f})")
        if results['consensus_cards']:
            print(f"🃏 Consensus Cards: {results['consensus_cards']}")
        if results['consensus_patterns']:
            print(f"🎯 Consensus Patterns: {results['consensus_patterns']}")
        print(f"🤝 Agreement Score: {results['agreement_score']:.2f}")

        print("=" * 50)
        print(f"⏳ Waiting {DELAY_NEXT} seconds for next round...")
        time.sleep(DELAY_NEXT)

if __name__ == "__main__":
    run()
