import cv2
import numpy as np
from datetime import datetime
from PIL import ImageGrab
from ultralytics import YOLO
import easyocr

# === CONFIG ===
# UPDATE THIS WITH YOUR GAME COORDINATES FROM find_game_coordinates.py
CROP_BOX = (970, 388, 1350, 632)  # Replace with your actual game coordinates

def test_single_detection():
    """Test detection on a single screenshot"""
    
    print("🎯 Simple Detection Test")
    print("=" * 40)
    print(f"📦 Using crop box: {CROP_BOX}")
    print("Make sure your card game is visible!")
    
    input("Press ENTER when ready to capture...")
    
    # Capture screenshot
    print("📸 Capturing screenshot...")
    img = ImageGrab.grab(bbox=CROP_BOX)
    img_np = np.array(img)
    img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
    
    # Save for inspection
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = f"test_capture_{timestamp}.jpg"
    cv2.imwrite(screenshot_path, img_bgr)
    print(f"✅ Screenshot saved: {screenshot_path}")
    print(f"📏 Image size: {img_bgr.shape}")
    
    # Test YOLO card detection
    print("\n🎯 Testing YOLO Card Detection...")
    try:
        card_model = YOLO("detect_yolo_one.pt")
        results = card_model(img_bgr, conf=0.3, verbose=False)
        
        if results[0].boxes:
            print(f"✅ Found {len(results[0].boxes)} detections:")
            for i, box in enumerate(results[0].boxes):
                cls_id = int(box.cls[0])
                confidence = float(box.conf[0])
                label = card_model.names[cls_id]
                print(f"   {i+1}. {label} ({confidence:.3f})")
        else:
            print("❌ No card detections found")
            print("💡 Try lowering confidence or check if game is visible")
    except Exception as e:
        print(f"❌ YOLO error: {e}")
    
    # Test winning seat detection
    print("\n🏆 Testing Winning Seat Detection...")
    try:
        seat_model = YOLO("winning_seat.pt")
        results = seat_model(img_bgr, conf=0.3, verbose=False)
        
        if results[0].boxes:
            print(f"✅ Found {len(results[0].boxes)} seat detections:")
            for i, box in enumerate(results[0].boxes):
                cls_id = int(box.cls[0])
                confidence = float(box.conf[0])
                label = seat_model.names[cls_id]
                xyxy = box.xyxy[0].cpu().numpy()
                x_center = (xyxy[0] + xyxy[2]) / 2
                
                # Determine seat position
                image_width = img_bgr.shape[1]
                if x_center < image_width / 3:
                    seat = "blue_seat (left)"
                elif x_center < 2 * image_width / 3:
                    seat = "purple_seat (center)"
                else:
                    seat = "golden_seat (right)"
                
                print(f"   {i+1}. {label} → {seat} ({confidence:.3f})")
        else:
            print("❌ No winning seat detections found")
    except Exception as e:
        print(f"❌ Winning seat error: {e}")
    
    # Test EasyOCR
    print("\n👁️ Testing EasyOCR...")
    try:
        reader = easyocr.Reader(['en'])
        results = reader.readtext(img_bgr)
        
        if results:
            print(f"✅ Found {len(results)} text regions:")
            for i, (bbox, text, confidence) in enumerate(results[:10]):  # Show first 10
                if confidence > 0.5:  # Only show confident detections
                    print(f"   {i+1}. '{text}' ({confidence:.3f})")
        else:
            print("❌ No text found")
    except Exception as e:
        print(f"❌ EasyOCR error: {e}")
    
    print(f"\n✅ Test complete! Check {screenshot_path} to see what was captured.")
    
    # Show the image
    try:
        cv2.imshow("Captured Game Area", img_bgr)
        print("👀 Image displayed. Press any key to close...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    except:
        print("💡 Could not display image, but it's saved to file.")

if __name__ == "__main__":
    test_single_detection()
