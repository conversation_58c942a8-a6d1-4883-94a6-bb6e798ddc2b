import sqlite3
import pandas as pd
from collections import Counter
import re

def view_consensus_results():
    """View the multi-model consensus detection results"""
    
    conn = sqlite3.connect("game_analysis.db")
    
    try:
        # Check if table exists
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='consensus_detections'")
        if not cursor.fetchone():
            print("❌ No consensus_detections table found. Run the consensus detector first.")
            return
        
        # Get all consensus detections
        df = pd.read_sql_query("""
            SELECT * FROM consensus_detections 
            ORDER BY timestamp DESC
        """, conn)
        
        if df.empty:
            print("❌ No consensus detections found in database.")
            return
        
        print(f"🔍 Found {len(df)} consensus detection rounds")
        print("=" * 80)
        
        # Show recent rounds with detailed breakdown
        for i, (_, row) in enumerate(df.head(3).iterrows()):
            print(f"\n🕐 Round {i+1}: {row['timestamp']}")
            print("-" * 60)
            
            # Winning seat
            print(f"🏆 Winning Seat: {row['winning_seat']} (confidence: {row['winning_seat_confidence']:.3f})")
            
            # Card detections comparison
            print("\n🃏 Card Detections:")
            print(f"   YOLO:      {row['yolo_cards'] or 'None'}")
            print(f"   PaddleOCR: {row['paddle_cards'] or 'None'}")
            print(f"   EasyOCR:   {row['easy_cards'] or 'None'}")
            print(f"   🤝 CONSENSUS: {row['consensus_cards'] or 'None'}")
            
            # Pattern detections comparison
            print("\n🎯 Pattern Detections:")
            print(f"   YOLO:      {row['yolo_patterns'] or 'None'}")
            print(f"   PaddleOCR: {row['paddle_patterns'] or 'None'}")
            print(f"   EasyOCR:   {row['easy_patterns'] or 'None'}")
            print(f"   🤝 CONSENSUS: {row['consensus_patterns'] or 'None'}")
            
            # Agreement metrics
            print(f"\n📊 Metrics:")
            print(f"   Agreement Score: {row['agreement_score']:.2f}")
            print(f"   Total Detections: {row['total_detections']}")
            print(f"   Consensus Detections: {row['consensus_detections']}")
            print(f"   Consensus Rate: {row['consensus_detections']/max(row['total_detections'], 1)*100:.1f}%")
        
        # Overall statistics
        print("\n📈 Overall Statistics:")
        print("=" * 50)
        
        # Winning seat distribution
        seat_counts = df['winning_seat'].value_counts()
        print("🏆 Winning Seat Distribution:")
        for seat, count in seat_counts.items():
            percentage = count / len(df) * 100
            print(f"   {seat}: {count} times ({percentage:.1f}%)")
        
        # Agreement score statistics
        print(f"\n🤝 Agreement Score Statistics:")
        print(f"   Average: {df['agreement_score'].mean():.3f}")
        print(f"   Range: {df['agreement_score'].min():.3f} - {df['agreement_score'].max():.3f}")
        print(f"   High Agreement (>0.7): {len(df[df['agreement_score'] > 0.7])} rounds ({len(df[df['agreement_score'] > 0.7])/len(df)*100:.1f}%)")
        
        # Consensus effectiveness
        print(f"\n🎯 Consensus Effectiveness:")
        avg_total = df['total_detections'].mean()
        avg_consensus = df['consensus_detections'].mean()
        print(f"   Average Total Detections: {avg_total:.1f}")
        print(f"   Average Consensus Detections: {avg_consensus:.1f}")
        print(f"   Average Consensus Rate: {avg_consensus/max(avg_total, 1)*100:.1f}%")
        
        # Most common consensus results
        print(f"\n🔥 Most Common Consensus Results:")
        
        # Cards
        all_consensus_cards = []
        for cards_str in df['consensus_cards'].dropna():
            if cards_str:
                cards = [card.strip() for card in cards_str.split(',')]
                all_consensus_cards.extend(cards)
        
        if all_consensus_cards:
            card_counts = Counter(all_consensus_cards)
            print("   Cards:")
            for card, count in card_counts.most_common(5):
                print(f"     {card}: {count} times")
        
        # Patterns
        all_consensus_patterns = []
        for patterns_str in df['consensus_patterns'].dropna():
            if patterns_str:
                patterns = [pattern.strip() for pattern in patterns_str.split(',')]
                all_consensus_patterns.extend(patterns)
        
        if all_consensus_patterns:
            pattern_counts = Counter(all_consensus_patterns)
            print("   Patterns:")
            for pattern, count in pattern_counts.most_common(5):
                print(f"     {pattern}: {count} times")
        
        # Model performance comparison
        print(f"\n⚖️ Model Performance Comparison:")
        
        def count_detections(column):
            total = 0
            for detection_str in df[column].dropna():
                if detection_str:
                    total += len([d.strip() for d in detection_str.split(',') if d.strip()])
            return total
        
        yolo_total = count_detections('yolo_cards') + count_detections('yolo_patterns')
        paddle_total = count_detections('paddle_cards') + count_detections('paddle_patterns')
        easy_total = count_detections('easy_cards') + count_detections('easy_patterns')
        
        print(f"   YOLO Total Detections: {yolo_total}")
        print(f"   PaddleOCR Total Detections: {paddle_total}")
        print(f"   EasyOCR Total Detections: {easy_total}")
        
        # Winning seat confidence analysis
        print(f"\n🎯 Winning Seat Confidence Analysis:")
        valid_confidences = df[df['winning_seat'] != 'unknown']['winning_seat_confidence']
        if not valid_confidences.empty:
            print(f"   Average Confidence: {valid_confidences.mean():.3f}")
            print(f"   Range: {valid_confidences.min():.3f} - {valid_confidences.max():.3f}")
            print(f"   High Confidence (>0.8): {len(valid_confidences[valid_confidences > 0.8])} detections")
        else:
            print("   No valid winning seat detections found")
        
    except Exception as e:
        print(f"❌ Error reading database: {e}")
    finally:
        conn.close()

def export_consensus_data():
    """Export consensus data to CSV for further analysis"""
    conn = sqlite3.connect("game_analysis.db")
    
    try:
        df = pd.read_sql_query("SELECT * FROM consensus_detections ORDER BY timestamp DESC", conn)
        
        if df.empty:
            print("❌ No data to export")
            return
        
        filename = f"consensus_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False)
        print(f"✅ Data exported to {filename}")
        
    except Exception as e:
        print(f"❌ Export error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔍 Multi-Model Consensus Results Viewer")
    print("=" * 60)
    
    view_consensus_results()
    
    print("\n" + "="*60)
    export_choice = input("Export data to CSV? (y/n): ").lower().strip()
    if export_choice == 'y':
        from datetime import datetime
        export_consensus_data()
