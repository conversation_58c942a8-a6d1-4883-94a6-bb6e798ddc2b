import sqlite3
import pandas as pd

def view_high_confidence_detections():
    """View the high-confidence detections from the database"""
    
    conn = sqlite3.connect("game_analysis.db")
    
    try:
        # Get all high-confidence detections
        df = pd.read_sql_query("""
            SELECT timestamp, label, confidence, seat, 
                   bbox_x1, bbox_y1, bbox_x2, bbox_y2
            FROM high_confidence_detections 
            ORDER BY timestamp DESC, confidence DESC
        """, conn)
        
        if df.empty:
            print("❌ No high-confidence detections found in database.")
            return
        
        print(f"📊 Found {len(df)} high-confidence detections")
        print("=" * 80)
        
        # Group by timestamp to show rounds
        for timestamp in df['timestamp'].unique()[:5]:  # Show last 5 rounds
            round_data = df[df['timestamp'] == timestamp]
            print(f"\n🕐 Round: {timestamp}")
            print("-" * 40)
            
            # Group by seat
            for seat in ['A', 'B', 'C']:
                seat_data = round_data[round_data['seat'] == seat]
                if not seat_data.empty:
                    detections = []
                    for _, row in seat_data.iterrows():
                        detections.append(f"{row['label']} ({row['confidence']:.3f})")
                    print(f"  Seat {seat}: {', '.join(detections)}")
                else:
                    print(f"  Seat {seat}: No high-confidence detections")
        
        # Show statistics
        print("\n📈 Detection Statistics:")
        print("-" * 40)
        print("By Class:")
        class_stats = df.groupby('label').agg({
            'confidence': ['count', 'mean', 'min', 'max']
        }).round(3)
        print(class_stats)
        
        print("\nBy Seat:")
        seat_stats = df.groupby('seat').agg({
            'confidence': ['count', 'mean']
        }).round(3)
        print(seat_stats)
        
        # Show confidence distribution
        print(f"\nConfidence Range: {df['confidence'].min():.3f} - {df['confidence'].max():.3f}")
        print(f"Average Confidence: {df['confidence'].mean():.3f}")
        
    except Exception as e:
        print(f"❌ Error reading database: {e}")
    finally:
        conn.close()

def check_model_classes():
    """Check what classes your YOLO model can detect"""
    try:
        from ultralytics import YOLO
        model = YOLO("detect_yolo_one.pt")
        print("🎯 Your YOLO model classes:")
        print("-" * 30)
        for i, class_name in model.names.items():
            print(f"  {i}: {class_name}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")

if __name__ == "__main__":
    print("🔍 YOLO High-Confidence Detection Viewer")
    print("=" * 50)
    
    # Check model classes first
    check_model_classes()
    print()
    
    # View detections
    view_high_confidence_detections()
