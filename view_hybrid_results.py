import sqlite3
import pandas as pd

def view_hybrid_detection_results():
    """View the hybrid YOLO + OCR detection results"""
    
    conn = sqlite3.connect("game_analysis.db")
    
    try:
        # Check if table exists
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='hybrid_detections'")
        if not cursor.fetchone():
            print("❌ No hybrid_detections table found. Run the hybrid detector first.")
            return
        
        # Get all hybrid detections
        df = pd.read_sql_query("""
            SELECT timestamp, label, confidence, seat, verification_method,
                   ocr_text, ocr_confidence, verified
            FROM hybrid_detections 
            ORDER BY timestamp DESC, confidence DESC
        """, conn)
        
        if df.empty:
            print("❌ No hybrid detections found in database.")
            return
        
        print(f"🔍 Found {len(df)} hybrid detections")
        print("=" * 80)
        
        # Show recent rounds
        for timestamp in df['timestamp'].unique()[:3]:  # Show last 3 rounds
            round_data = df[df['timestamp'] == timestamp]
            print(f"\n🕐 Round: {timestamp}")
            print("-" * 50)
            
            # Group by verification method
            direct_detections = round_data[round_data['verification_method'] == 'high_accuracy_direct']
            ocr_detections = round_data[round_data['verification_method'] == 'ocr_verified']
            
            if not direct_detections.empty:
                print("✅ Direct Detections (High Accuracy):")
                for _, row in direct_detections.iterrows():
                    print(f"   Seat {row['seat']}: {row['label']} ({row['confidence']:.3f})")
            
            if not ocr_detections.empty:
                print("🔍 OCR Verified Detections:")
                for _, row in ocr_detections.iterrows():
                    status = "✅" if row['verified'] else "❌"
                    print(f"   {status} Seat {row['seat']}: {row['label']} ({row['confidence']:.3f}) → '{row['ocr_text']}' ({row['ocr_confidence']:.3f})")
        
        # Show verification statistics
        print("\n📈 Verification Statistics:")
        print("-" * 50)
        
        # Overall stats
        total_detections = len(df)
        direct_count = len(df[df['verification_method'] == 'high_accuracy_direct'])
        ocr_attempted = len(df[df['verification_method'] == 'ocr_verified'])
        ocr_verified = len(df[(df['verification_method'] == 'ocr_verified') & (df['verified'] == True)])
        
        print(f"Total Detections: {total_detections}")
        print(f"Direct (High Accuracy): {direct_count} ({direct_count/total_detections*100:.1f}%)")
        print(f"OCR Attempted: {ocr_attempted} ({ocr_attempted/total_detections*100:.1f}%)")
        print(f"OCR Verified: {ocr_verified} ({ocr_verified/ocr_attempted*100:.1f}% success rate)" if ocr_attempted > 0 else "OCR Verified: 0")
        
        # Class-wise verification success
        print("\n🎯 Class-wise OCR Verification Success:")
        print("-" * 40)
        ocr_data = df[df['verification_method'] == 'ocr_verified']
        if not ocr_data.empty:
            class_stats = ocr_data.groupby('label').agg({
                'verified': ['count', 'sum']
            })
            class_stats.columns = ['Total_Attempts', 'Successful']
            class_stats['Success_Rate'] = (class_stats['Successful'] / class_stats['Total_Attempts'] * 100).round(1)
            print(class_stats)
        
        # Show confidence distributions
        print("\n📊 Confidence Distributions:")
        print("-" * 40)
        print("YOLO Confidence:")
        print(f"  Range: {df['confidence'].min():.3f} - {df['confidence'].max():.3f}")
        print(f"  Average: {df['confidence'].mean():.3f}")
        
        ocr_data_verified = df[(df['verification_method'] == 'ocr_verified') & (df['verified'] == True)]
        if not ocr_data_verified.empty:
            print("OCR Confidence (for verified detections):")
            print(f"  Range: {ocr_data_verified['ocr_confidence'].min():.3f} - {ocr_data_verified['ocr_confidence'].max():.3f}")
            print(f"  Average: {ocr_data_verified['ocr_confidence'].mean():.3f}")
        
        # Show common OCR texts
        print("\n📝 Common OCR Verification Results:")
        print("-" * 40)
        ocr_texts = df[df['verification_method'] == 'ocr_verified']['ocr_text'].value_counts().head(10)
        for text, count in ocr_texts.items():
            print(f"  '{text}': {count} times")
        
    except Exception as e:
        print(f"❌ Error reading database: {e}")
    finally:
        conn.close()

def compare_detection_methods():
    """Compare different detection methods"""
    conn = sqlite3.connect("game_analysis.db")
    
    try:
        # Check what tables exist
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("📊 Available Detection Tables:")
        print("-" * 30)
        for table in tables:
            if 'detection' in table.lower() or 'card' in table.lower():
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} records")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔍 Hybrid YOLO + OCR Detection Results Viewer")
    print("=" * 60)
    
    # Show available tables
    compare_detection_methods()
    print()
    
    # View hybrid results
    view_hybrid_detection_results()
