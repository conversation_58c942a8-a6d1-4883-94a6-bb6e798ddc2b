import os
import cv2
import time
import numpy as np
from datetime import datetime
from PIL import Image<PERSON>rab
from ultralytics import YOLO
import sqlite3

# === CONFIG ===
CROP_BOX = (970, 388, 1350, 632)
MODEL_PATH = "detect_yolo_one.pt"  # Your trained YOLOv8 model
DB_PATH = "game_analysis.db"
TABLE_NAME = "high_confidence_detections"
DELAY_INITIAL = 14.5
DELAY_NEXT = 56
CONFIDENCE_THRESHOLD = 0.90  # Only collect detections above 90% confidence

# High-accuracy classes (>90% precision from your metrics)
HIGH_ACCURACY_CLASSES = {
    'hearts', 'high_card', '8', '9', 'A', 'J', 'blue_seat', 'spades'
}

# === Load Model ===
model = YOLO(MODEL_PATH)

# === SQLite DB ===
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# Create table for high-confidence detections
cursor.execute(f'''
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp TEXT,
        label TEXT,
        confidence REAL,
        seat TEXT,
        bbox_x1 REAL,
        bbox_y1 REAL,
        bbox_x2 REAL,
        bbox_y2 REAL
    )
''')
conn.commit()

# === CARD MAPPING HELPERS ===
def get_seat_from_x(x):
    if x < 110: return 'A'
    elif x < 230: return 'B'
    else: return 'C'

def extract_data_from_detections(results):
    seats = {'A': [], 'B': [], 'C': []}
    filtered_detections = []

    for box in results[0].boxes:
        cls_id = int(box.cls[0])
        confidence = float(box.conf[0])
        label = model.names[cls_id]
        xyxy = box.xyxy[0].cpu().numpy()
        x_center = (xyxy[0] + xyxy[2]) / 2

        # Filter by confidence threshold AND high-accuracy classes
        if confidence >= CONFIDENCE_THRESHOLD and label in HIGH_ACCURACY_CLASSES:
            seat = get_seat_from_x(x_center)
            seats[seat].append(label)
            filtered_detections.append({
                'label': label,
                'confidence': confidence,
                'seat': seat,
                'bbox': xyxy
            })
            print(f"✅ High-confidence detection: {label} ({confidence:.3f}) in seat {seat}")
        else:
            print(f"❌ Filtered out: {label} ({confidence:.3f}) - Low confidence or accuracy")

    return seats, filtered_detections

def save_to_db(filtered_detections, timestamp):
    """Save only high-confidence detections to database"""
    if not filtered_detections:
        print(f"⚠️ No high-confidence detections to save at {timestamp}")
        return

    for detection in filtered_detections:
        cursor.execute(f'''
            INSERT INTO {TABLE_NAME}
            (timestamp, label, confidence, seat, bbox_x1, bbox_y1, bbox_x2, bbox_y2)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            detection['label'],
            detection['confidence'],
            detection['seat'],
            detection['bbox'][0],  # x1
            detection['bbox'][1],  # y1
            detection['bbox'][2],  # x2
            detection['bbox'][3]   # y2
        ))

    conn.commit()
    print(f"✅ Saved {len(filtered_detections)} high-confidence detections at {timestamp}")

    # Print summary by seat
    seat_summary = {}
    for det in filtered_detections:
        seat = det['seat']
        if seat not in seat_summary:
            seat_summary[seat] = []
        seat_summary[seat].append(f"{det['label']}({det['confidence']:.2f})")

    for seat, detections in seat_summary.items():
        print(f"   Seat {seat}: {', '.join(detections)}")

# === MAIN LOOP ===
def run():
    print("Press ENTER to start capture loop after countdown reaches 1...")
    input()
    time.sleep(DELAY_INITIAL)

    while True:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        img = ImageGrab.grab(bbox=CROP_BOX)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

        results = model(img_bgr, verbose=False, conf=CONFIDENCE_THRESHOLD)
        seats, filtered_detections = extract_data_from_detections(results)
        save_to_db(filtered_detections, timestamp)

        print(f"📊 Total detections: {len(results[0].boxes) if results[0].boxes is not None else 0}")
        print(f"🎯 High-confidence detections: {len(filtered_detections)}")
        print("-" * 50)

        time.sleep(DELAY_NEXT)

if __name__ == "__main__":
    run()
