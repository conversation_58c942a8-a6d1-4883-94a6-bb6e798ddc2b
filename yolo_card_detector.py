import os
import cv2
import time
import numpy as np
from datetime import datetime
from PIL import ImageGrab
from ultralytics import YOLO
import sqlite3

# === CONFIG ===
CROP_BOX = (970, 388, 1350, 632)
MODEL_PATH = "detect_yolo_one.pt"  # Your trained YOLOv8 model
DB_PATH = "game_analysis.db"
TABLE_NAME = "high_confidence_detections"
DELAY_INITIAL = 14.5
DELAY_NEXT = 56
CONFIDENCE_THRESHOLD = 0.95  # Only collect detections above 95% confidence

# === Load Model ===
model = YOLO(MODEL_PATH)

# === SQLite DB ===
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# === CARD MAPPING HELPERS ===
def get_seat_from_x(x):
    if x < 110: return 'A'
    elif x < 230: return 'B'
    else: return 'C'

def extract_data_from_detections(results):
    seats = {'A': [], 'B': [], 'C': []}

    for box in results[0].boxes:
        cls_id = int(box.cls[0])
        label = model.names[cls_id]
        xyxy = box.xyxy[0].cpu().numpy()
        x_center = (xyxy[0] + xyxy[2]) / 2

        seat = get_seat_from_x(x_center)
        seats[seat].append(label)

    return seats

def save_to_db(seats, timestamp):
    def split_card(card):
        digit = card[:-1]
        suit = card[-1]
        color = 'red' if suit in ['♥', '♦'] else 'black'
        return digit, suit, color

    data = {}
    for s in ['A', 'B', 'C']:
        cards = seats.get(s, [])[:3]
        for i in range(3):
            if i < len(cards):
                digit, suit, color = split_card(cards[i])
                data[f"{s.lower()}_card_{i+1}"] = digit
                data[f"{s.lower()}_suit_{i+1}"] = suit
                data[f"{s.lower()}_color_{i+1}"] = color
            else:
                data[f"{s.lower()}_card_{i+1}"] = '?'
                data[f"{s.lower()}_suit_{i+1}"] = '?'
                data[f"{s.lower()}_color_{i+1}"] = '?'

    query = f"""
    INSERT INTO {TABLE_NAME} (
        round_time, a_card_1, a_suit_1, a_color_1, a_card_2, a_suit_2, a_color_2, a_card_3, a_suit_3, a_color_3,
        b_card_1, b_suit_1, b_color_1, b_card_2, b_suit_2, b_color_2, b_card_3, b_suit_3, b_color_3,
        c_card_1, c_suit_1, c_color_1, c_card_2, c_suit_2, c_color_2, c_card_3, c_suit_3, c_color_3
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """

    values = [timestamp] + list(data.values())
    cursor.execute(query, values)
    conn.commit()
    print(f"✅ Data saved at {timestamp}")

# === MAIN LOOP ===
def run():
    print("Press ENTER to start capture loop after countdown reaches 1...")
    input()
    time.sleep(DELAY_INITIAL)

    while True:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        img = ImageGrab.grab(bbox=CROP_BOX)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

        results = model(img_bgr, verbose=False)
        seats = extract_data_from_detections(results)
        save_to_db(seats, timestamp)

        time.sleep(DELAY_NEXT)

if __name__ == "__main__":
    run()
